package com.translatedemo.services

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.util.Log
import android.view.*
import android.widget.Button
import android.widget.LinearLayout
import android.widget.Toast
import com.translatedemo.R
import com.translatedemo.utils.PermissionHelper
import com.translatedemo.utils.TranslationActionHandler

class OverlayManager(
    private val context: Context,
    private val windowManager: WindowManager
) {

    companion object {
        private const val TAG = "OverlayManager"
    }

    private var actionMenuView: View? = null
    private var translationHandler: TranslationActionHandler? = null

    init {
        translationHandler = TranslationActionHandler(context)
    }
    
    fun showActionMenu() {
        Log.d(TAG, "showActionMenu called")

        if (actionMenuView != null) {
            Log.d(TAG, "Action menu already visible, hiding it")
            hideActionMenu()
            return
        }

        // Check if accessibility service is enabled
        if (!PermissionHelper.isAccessibilityServiceEnabled(context)) {
            Toast.makeText(context, "Please enable accessibility service first", Toast.LENGTH_LONG).show()
            Log.w(TAG, "Accessibility service not enabled")
            return
        }

        try {
            Log.d(TAG, "Creating and showing action menu")
            actionMenuView = createActionMenu()
            val params = createActionMenuParams()
            windowManager.addView(actionMenuView, params)
            Log.d(TAG, "Action menu displayed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to show action menu", e)
            Toast.makeText(context, "Failed to show action menu: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    fun hideActionMenu() {
        actionMenuView?.let { view ->
            try {
                windowManager.removeView(view)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        actionMenuView = null
    }
    
    fun cleanup() {
        hideActionMenu()
        translationHandler?.cleanup()
    }
    
    private fun createActionMenu(): View {
        val layout = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            setBackgroundResource(android.R.drawable.dialog_holo_light_frame)
            setPadding(16, 16, 16, 16)
        }

        // Copy Translation Button
        val copyButton = Button(context).apply {
            text = context.getString(R.string.action_copy_translation)
            setOnClickListener {
                handleCopyTranslation()
                hideActionMenu()
            }
        }

        // Replace Text Button
        val replaceButton = Button(context).apply {
            text = context.getString(R.string.action_replace_text)
            setOnClickListener {
                handleReplaceText()
                hideActionMenu()
            }
        }

        // Close Button
        val closeButton = Button(context).apply {
            text = context.getString(R.string.action_close)
            setOnClickListener {
                hideActionMenu()
            }
        }

        layout.addView(copyButton)
        layout.addView(replaceButton)
        layout.addView(closeButton)

        // Make the menu draggable
        makeDraggable(layout)

        return layout
    }

    private fun makeDraggable(view: View) {
        var initialX = 0
        var initialY = 0
        var initialTouchX = 0f
        var initialTouchY = 0f
        var isDragging = false

        view.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    val params = view.layoutParams as WindowManager.LayoutParams
                    initialX = params.x
                    initialY = params.y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    isDragging = false
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    val deltaX = event.rawX - initialTouchX
                    val deltaY = event.rawY - initialTouchY

                    // Only start dragging if moved more than 10 pixels
                    if (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10) {
                        isDragging = true
                        val params = view.layoutParams as WindowManager.LayoutParams
                        params.x = initialX + deltaX.toInt()
                        params.y = initialY + deltaY.toInt()
                        windowManager.updateViewLayout(view, params)
                    }
                    true
                }
                MotionEvent.ACTION_UP -> {
                    // Don't consume the event if it wasn't a drag, so buttons can still be clicked
                    isDragging
                }
                else -> false
            }
        }
    }
    
    private fun createActionMenuParams(): WindowManager.LayoutParams {
        val layoutFlag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }
        
        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            layoutFlag,
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.CENTER
        }
    }
    
    private fun handleCopyTranslation() {
        try {
            Log.d(TAG, "handleCopyTranslation called")
            translationHandler?.copySelectedTextTranslation { success, message ->
                try {
                    Log.d(TAG, "Translation callback: success=$success, message=$message")
                    Toast.makeText(context, message, Toast.LENGTH_LONG).show()
                } catch (e: Exception) {
                    Log.e(TAG, "Error showing toast", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in handleCopyTranslation", e)
            try {
                Toast.makeText(context, "Translation error occurred", Toast.LENGTH_SHORT).show()
            } catch (e2: Exception) {
                Log.e(TAG, "Error showing error toast", e2)
            }
        }
    }

    private fun handleReplaceText() {
        try {
            Log.d(TAG, "handleReplaceText called")
            translationHandler?.replaceSelectedTextWithTranslation { success, message ->
                try {
                    Log.d(TAG, "Replace callback: success=$success, message=$message")
                    Toast.makeText(context, message, Toast.LENGTH_LONG).show()
                } catch (e: Exception) {
                    Log.e(TAG, "Error showing toast", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in handleReplaceText", e)
            try {
                Toast.makeText(context, "Replace text error occurred", Toast.LENGTH_SHORT).show()
            } catch (e2: Exception) {
                Log.e(TAG, "Error showing error toast", e2)
            }
        }
    }
}
