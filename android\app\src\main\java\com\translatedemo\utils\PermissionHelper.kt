package com.translatedemo.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.text.TextUtils
import androidx.appcompat.app.AlertDialog
import com.translatedemo.R

object PermissionHelper {
    
    const val REQUEST_OVERLAY_PERMISSION = 1001
    const val REQUEST_ACCESSIBILITY_PERMISSION = 1002
    
    /**
     * Check if the app has overlay permission
     */
    fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true // Permission not required for older versions
        }
    }
    
    /**
     * Check if accessibility service is enabled
     */
    fun isAccessibilityServiceEnabled(context: Context): Boolean {
        val accessibilityEnabled = try {
            Settings.Secure.getInt(
                context.contentResolver,
                Settings.Secure.ACCESSIBILITY_ENABLED
            )
        } catch (e: Settings.SettingNotFoundException) {
            0
        }
        
        if (accessibilityEnabled == 1) {
            val services = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )
            
            if (!services.isNullOrEmpty()) {
                val colonSplitter = TextUtils.SimpleStringSplitter(':')
                colonSplitter.setString(services)
                
                while (colonSplitter.hasNext()) {
                    val componentName = colonSplitter.next()
                    if (componentName.equals(
                            "${context.packageName}/com.translatedemo.services.AccessibilityTranslateService",
                            ignoreCase = true
                        )) {
                        return true
                    }
                }
            }
        }
        
        return false
    }
    
    /**
     * Request overlay permission
     */
    fun requestOverlayPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(activity)) {
                showOverlayPermissionDialog(activity) {
                    val intent = Intent(
                        Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                        Uri.parse("package:${activity.packageName}")
                    )
                    activity.startActivityForResult(intent, REQUEST_OVERLAY_PERMISSION)
                }
            }
        }
    }
    
    /**
     * Request accessibility permission
     */
    fun requestAccessibilityPermission(activity: Activity) {
        if (!isAccessibilityServiceEnabled(activity)) {
            showAccessibilityPermissionDialog(activity) {
                val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
                activity.startActivityForResult(intent, REQUEST_ACCESSIBILITY_PERMISSION)
            }
        }
    }
    
    /**
     * Check and request all required permissions
     */
    fun checkAndRequestPermissions(activity: Activity, callback: (Boolean) -> Unit) {
        val hasOverlay = hasOverlayPermission(activity)
        val hasAccessibility = isAccessibilityServiceEnabled(activity)
        
        when {
            !hasOverlay -> {
                requestOverlayPermission(activity)
                callback(false)
            }
            !hasAccessibility -> {
                requestAccessibilityPermission(activity)
                callback(false)
            }
            else -> {
                callback(true)
            }
        }
    }
    
    private fun showOverlayPermissionDialog(activity: Activity, onPositive: () -> Unit) {
        AlertDialog.Builder(activity)
            .setTitle(activity.getString(R.string.permission_overlay_title))
            .setMessage(activity.getString(R.string.permission_overlay_message))
            .setPositiveButton("Settings") { _, _ -> onPositive() }
            .setNegativeButton("Cancel", null)
            .setCancelable(false)
            .show()
    }
    
    private fun showAccessibilityPermissionDialog(activity: Activity, onPositive: () -> Unit) {
        AlertDialog.Builder(activity)
            .setTitle(activity.getString(R.string.permission_accessibility_title))
            .setMessage(activity.getString(R.string.permission_accessibility_message))
            .setPositiveButton("Settings") { _, _ -> onPositive() }
            .setNegativeButton("Cancel", null)
            .setCancelable(false)
            .show()
    }
    
    /**
     * Handle permission result
     */
    fun handlePermissionResult(
        activity: Activity,
        requestCode: Int,
        callback: (Boolean, String) -> Unit
    ) {
        when (requestCode) {
            REQUEST_OVERLAY_PERMISSION -> {
                val granted = hasOverlayPermission(activity)
                val message = if (granted) {
                    activity.getString(R.string.permission_granted)
                } else {
                    activity.getString(R.string.permission_denied)
                }
                callback(granted, message)
            }
            REQUEST_ACCESSIBILITY_PERMISSION -> {
                val granted = isAccessibilityServiceEnabled(activity)
                val message = if (granted) {
                    activity.getString(R.string.permission_granted)
                } else {
                    activity.getString(R.string.permission_denied)
                }
                callback(granted, message)
            }
        }
    }
}
