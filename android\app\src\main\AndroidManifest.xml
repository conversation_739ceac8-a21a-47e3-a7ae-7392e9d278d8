<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET" />

    <!-- Floating overlay permissions -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />

    <!-- Accessibility service permission -->
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />

    <!-- Clipboard access -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <!-- Auto-start permission for some devices -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <!-- Wake lock to keep service running -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme"
      android:supportsRtl="true">
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
      </activity>

      <!-- Floating Overlay Service -->
      <service
        android:name=".services.FloatingOverlayService"
        android:enabled="true"
        android:exported="false"
        android:foregroundServiceType="specialUse">
        <property
          android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
          android:value="translation_overlay" />
      </service>

      <!-- Accessibility Service -->
      <service
        android:name=".services.AccessibilityTranslateService"
        android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
        android:exported="true">
        <intent-filter>
          <action android:name="android.accessibilityservice.AccessibilityService" />
        </intent-filter>
        <meta-data
          android:name="android.accessibilityservice"
          android:resource="@xml/accessibility_service_config" />
      </service>

      <!-- Boot receiver to restart service -->
      <receiver
        android:name=".receivers.BootReceiver"
        android:enabled="true"
        android:exported="true">
        <intent-filter android:priority="1000">
          <action android:name="android.intent.action.BOOT_COMPLETED" />
          <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
          <action android:name="android.intent.action.PACKAGE_REPLACED" />
          <data android:scheme="package" />
        </intent-filter>
      </receiver>

    </application>
</manifest>
