package com.translatedemo;

import android.util.Log;

public class TranslateModule {
    private static final String TAG = "TranslateModule";
    private static boolean libraryLoaded = false;

    static {
        try {
            System.loadLibrary("translatemodule");
            libraryLoaded = true;
            Log.d(TAG, "Native library loaded successfully");
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load native library", e);
            libraryLoaded = false;
        }
    }

    public static boolean isLibraryLoaded() {
        return libraryLoaded;
    }

    public static String translateText(String input) {
        if (!libraryLoaded) {
            Log.w(TAG, "Native library not loaded, using fallback");
            return getFallbackTranslation(input);
        }

        try {
            return nativeTranslateText(input);
        } catch (Exception e) {
            Log.e(TAG, "Native translation failed", e);
            return getFallbackTranslation(input);
        }
    }

    public static String[] getSuggestions(String input, int maxSuggestions) {
        if (!libraryLoaded) {
            return new String[]{getFallbackTranslation(input)};
        }

        try {
            return nativeGetSuggestions(input, maxSuggestions);
        } catch (Exception e) {
            Log.e(TAG, "Native suggestions failed", e);
            return new String[]{getFallbackTranslation(input)};
        }
    }

    public static String transliterateWithContext(String input, String context) {
        if (!libraryLoaded) {
            return getFallbackTranslation(input);
        }

        try {
            return nativeTransliterateWithContext(input, context);
        } catch (Exception e) {
            Log.e(TAG, "Native context translation failed", e);
            return getFallbackTranslation(input);
        }
    }

    public static boolean isWordInDictionary(String word) {
        if (!libraryLoaded) {
            return false;
        }

        try {
            return nativeIsWordInDictionary(word);
        } catch (Exception e) {
            Log.e(TAG, "Native dictionary check failed", e);
            return false;
        }
    }

    private static String getFallbackTranslation(String input) {
        // Simple fallback translations
        String lowerInput = input.toLowerCase().trim();
        switch (lowerInput) {
            case "hello": return "வணக்கம்";
            case "good": return "நல்ல";
            case "morning": return "காலை";
            case "evening": return "மாலை";
            case "night": return "இரவு";
            case "thank you": return "நன்றி";
            case "yes": return "ஆம்";
            case "no": return "இல்லை";
            case "water": return "தண்ணீர்";
            case "food": return "உணவு";
            default: return "Translation: " + input;
        }
    }

    // Native methods
    private static native String nativeTranslateText(String input);
    private static native String[] nativeGetSuggestions(String input, int maxSuggestions);
    private static native String nativeTransliterateWithContext(String input, String context);
    private static native boolean nativeIsWordInDictionary(String word);
}
