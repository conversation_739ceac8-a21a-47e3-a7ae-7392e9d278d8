package com.translatedemo

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate
import com.translatedemo.services.FloatingOverlayService
import com.translatedemo.utils.PermissionHelper

class MainActivity : ReactActivity() {

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = "TranslateDemo"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate =
      DefaultReactActivityDelegate(this, mainComponentName, fabricEnabled)

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    // Check and request permissions when app starts
    checkPermissionsAndStartService()
  }

  private fun checkPermissionsAndStartService() {
    PermissionHelper.checkAndRequestPermissions(this) { allGranted ->
      if (allGranted) {
        startTranslationService()
      }
    }
  }

  private fun startTranslationService() {
    try {
      FloatingOverlayService.startService(this)
      Toast.makeText(this, getString(R.string.service_started), Toast.LENGTH_SHORT).show()
    } catch (e: Exception) {
      Toast.makeText(this, "Failed to start translation service", Toast.LENGTH_SHORT).show()
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)

    PermissionHelper.handlePermissionResult(this, requestCode) { granted, message ->
      Toast.makeText(this, message, Toast.LENGTH_SHORT).show()

      if (granted) {
        // Check if all permissions are now granted
        checkPermissionsAndStartService()
      }
    }
  }

  override fun onResume() {
    super.onResume()

    // Check permissions again when returning to app
    if (PermissionHelper.hasOverlayPermission(this) &&
        PermissionHelper.isAccessibilityServiceEnabled(this)) {

      // Ensure service is running
      if (!isServiceRunning()) {
        startTranslationService()
      }
    }
  }

  private fun isServiceRunning(): Boolean {
    // Simple check - in a real app you might want to use ActivityManager
    return true // Simplified for now
  }
}
