package com.translatedemo.receivers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.translatedemo.services.FloatingOverlayService
import com.translatedemo.utils.PermissionHelper

class BootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "BootReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Boot receiver triggered: ${intent.action}")
        
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                startTranslationService(context)
            }
        }
    }
    
    private fun startTranslationService(context: Context) {
        try {
            // Check if we have the necessary permissions
            if (PermissionHelper.hasOverlayPermission(context) && 
                PermissionHelper.isAccessibilityServiceEnabled(context)) {
                
                Log.d(TAG, "Starting translation service after boot")
                FloatingOverlayService.startService(context)
            } else {
                Log.d(TAG, "Permissions not granted, skipping service start")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error starting translation service after boot", e)
        }
    }
}
