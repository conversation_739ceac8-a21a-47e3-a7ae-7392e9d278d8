package com.translatedemo.utils

import android.content.Context
import android.util.Log
import com.translatedemo.R
import com.translatedemo.services.AccessibilityTranslateService

class TranslationActionHandler(private val context: Context) {
    
    companion object {
        private const val TAG = "TranslationActionHandler"
    }
    
    /**
     * Copy selected text translation to clipboard
     */
    fun copySelectedTextTranslation(callback: (<PERSON><PERSON><PERSON>, String) -> Unit) {
        try {
            Log.d(TAG, "copySelectedTextTranslation called")

            val accessibilityService = AccessibilityTranslateService.getInstance()

            if (accessibilityService == null) {
                Log.w(TAG, "Accessibility service instance is null")
                callback(false, "Accessibility service not available. Please restart the app.")
                return
            }

            if (!AccessibilityTranslateService.isServiceEnabled()) {
                Log.w(TAG, "Accessibility service not enabled")
                callback(false, "Please enable accessibility service in Settings → Accessibility → TranslateDemo")
                return
            }

            // Call the service with additional error handling
            try {
                accessibilityService.translateAndCopyText { success, message ->
                    try {
                        Log.d(TAG, "Translation callback received: success=$success, message=$message")
                        callback(success, message)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error in translation callback", e)
                        callback(false, "Translation callback error")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error calling translateAndCopyText", e)
                callback(false, "Translation service error: ${e.message}")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Critical error in copySelectedTextTranslation", e)
            callback(false, "Critical translation error. Please restart the app.")
        }
    }
    
    /**
     * Replace selected text with translation
     */
    fun replaceSelectedTextWithTranslation(callback: (Boolean, String) -> Unit) {
        val accessibilityService = AccessibilityTranslateService.getInstance()
        
        if (accessibilityService == null) {
            callback(false, context.getString(R.string.permission_accessibility_title))
            return
        }
        
        if (!AccessibilityTranslateService.isServiceEnabled()) {
            callback(false, "Accessibility service not enabled")
            return
        }
        
        try {
            accessibilityService.translateAndReplaceText { success, message ->
                Log.d(TAG, "Replace text result: $success, $message")
                callback(success, message)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error replacing text", e)
            callback(false, context.getString(R.string.translation_failed))
        }
    }
    
    /**
     * Get current selected text without translation
     */
    fun getCurrentSelectedText(): String {
        val accessibilityService = AccessibilityTranslateService.getInstance()
        return accessibilityService?.getCurrentSelectedText() ?: ""
    }
    
    /**
     * Check if accessibility service is available and enabled
     */
    fun isAccessibilityServiceAvailable(): Boolean {
        return AccessibilityTranslateService.isServiceEnabled()
    }
    
    /**
     * Cleanup resources
     */
    fun cleanup() {
        // Cleanup any resources if needed
        Log.d(TAG, "TranslationActionHandler cleanup")
    }
}
