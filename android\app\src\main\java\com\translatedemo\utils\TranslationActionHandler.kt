package com.translatedemo.utils

import android.content.Context
import android.util.Log
import com.translatedemo.R
import com.translatedemo.services.AccessibilityTranslateService

class TranslationActionHandler(private val context: Context) {
    
    companion object {
        private const val TAG = "TranslationActionHandler"
    }
    
    /**
     * Copy selected text translation to clipboard
     */
    fun copySelectedTextTranslation(callback: (<PERSON><PERSON><PERSON>, String) -> Unit) {
        val accessibilityService = AccessibilityTranslateService.getInstance()
        
        if (accessibilityService == null) {
            callback(false, context.getString(R.string.permission_accessibility_title))
            return
        }
        
        if (!AccessibilityTranslateService.isServiceEnabled()) {
            callback(false, "Accessibility service not enabled")
            return
        }
        
        try {
            accessibilityService.translateAndCopyText { success, message ->
                Log.d(TAG, "Copy translation result: $success, $message")
                callback(success, message)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error copying translation", e)
            callback(false, context.getString(R.string.translation_failed))
        }
    }
    
    /**
     * Replace selected text with translation
     */
    fun replaceSelectedTextWithTranslation(callback: (<PERSON><PERSON><PERSON>, String) -> Unit) {
        val accessibilityService = AccessibilityTranslateService.getInstance()
        
        if (accessibilityService == null) {
            callback(false, context.getString(R.string.permission_accessibility_title))
            return
        }
        
        if (!AccessibilityTranslateService.isServiceEnabled()) {
            callback(false, "Accessibility service not enabled")
            return
        }
        
        try {
            accessibilityService.translateAndReplaceText { success, message ->
                Log.d(TAG, "Replace text result: $success, $message")
                callback(success, message)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error replacing text", e)
            callback(false, context.getString(R.string.translation_failed))
        }
    }
    
    /**
     * Get current selected text without translation
     */
    fun getCurrentSelectedText(): String {
        val accessibilityService = AccessibilityTranslateService.getInstance()
        return accessibilityService?.getCurrentSelectedText() ?: ""
    }
    
    /**
     * Check if accessibility service is available and enabled
     */
    fun isAccessibilityServiceAvailable(): Boolean {
        return AccessibilityTranslateService.isServiceEnabled()
    }
    
    /**
     * Cleanup resources
     */
    fun cleanup() {
        // Cleanup any resources if needed
        Log.d(TAG, "TranslationActionHandler cleanup")
    }
}
