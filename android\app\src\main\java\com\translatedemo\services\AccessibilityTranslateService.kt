package com.translatedemo.services

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import com.translatedemo.TranslateModule

class AccessibilityTranslateService : AccessibilityService() {
    
    companion object {
        private const val TAG = "AccessibilityTranslateService"
        private var instance: AccessibilityTranslateService? = null
        
        fun getInstance(): AccessibilityTranslateService? = instance
        
        fun isServiceEnabled(): Boolean = instance != null
    }
    
    private var lastSelectedText: String = ""
    private var clipboardManager: ClipboardManager? = null
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        clipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        Log.d(TAG, "Accessibility service created")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
        Log.d(TAG, "Accessibility service destroyed")
    }
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        Log.d(TAG, "Accessibility service connected")
        
        val info = AccessibilityServiceInfo().apply {
            eventTypes = AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED or
                        AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED or
                        AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED
            
            feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC
            flags = AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS or
                   AccessibilityServiceInfo.FLAG_RETRIEVE_INTERACTIVE_WINDOWS
            
            notificationTimeout = 100
        }
        
        serviceInfo = info
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        event?.let { handleAccessibilityEvent(it) }
    }
    
    override fun onInterrupt() {
        Log.d(TAG, "Accessibility service interrupted")
    }
    
    private fun handleAccessibilityEvent(event: AccessibilityEvent) {
        when (event.eventType) {
            AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED -> {
                handleTextSelection(event)
            }
            AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED -> {
                // Handle text changes if needed
            }
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                // Handle window content changes if needed
            }
        }
    }
    
    private fun handleTextSelection(event: AccessibilityEvent) {
        try {
            val selectedText = getSelectedText(event)
            if (selectedText.isNotEmpty() && selectedText != lastSelectedText) {
                lastSelectedText = selectedText
                Log.d(TAG, "Text selected: $selectedText")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling text selection", e)
        }
    }
    
    private fun getSelectedText(event: AccessibilityEvent): String {
        // Try to get selected text from the event
        val fromIndex = event.fromIndex
        val toIndex = event.toIndex
        val text = event.text?.firstOrNull()?.toString() ?: ""
        
        return if (fromIndex >= 0 && toIndex > fromIndex && toIndex <= text.length) {
            text.substring(fromIndex, toIndex)
        } else {
            // Fallback: try to get selected text from accessibility node
            getSelectedTextFromNode(event.source)
        }
    }
    
    private fun getSelectedTextFromNode(node: AccessibilityNodeInfo?): String {
        if (node == null) return ""

        try {
            Log.d(TAG, "Attempting to get selected text from node")

            // Method 1: Try to find focused editable node with selection
            val focusedNode = findFocusedEditableNode(node)
            if (focusedNode != null) {
                val selectedText = getTextSelectionFromNode(focusedNode)
                if (selectedText.isNotEmpty()) {
                    Log.d(TAG, "Found selected text from focused node: '$selectedText'")
                    return selectedText
                }
            }

            // Method 2: Try to copy selected text
            val originalClipboard = getClipboardText()
            if (node.performAction(AccessibilityNodeInfo.ACTION_COPY)) {
                Thread.sleep(200) // Give more time for copy operation
                val newClipboard = getClipboardText()

                // Check if clipboard changed (indicating something was copied)
                if (newClipboard != originalClipboard && newClipboard.isNotEmpty()) {
                    Log.d(TAG, "Got text from copy action: '$newClipboard'")
                    return newClipboard
                }
            }

            // Method 3: Get all text from node (fallback)
            val allText = node.text?.toString() ?: ""
            Log.d(TAG, "Fallback to all node text: '$allText'")
            return allText

        } catch (e: Exception) {
            Log.e(TAG, "Error getting selected text from node", e)
            return ""
        } finally {
            node?.recycle()
        }
    }

    private fun findFocusedEditableNode(root: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        if (root.isFocused && root.isEditable) {
            return root
        }

        for (i in 0 until root.childCount) {
            val child = root.getChild(i)
            if (child != null) {
                val result = findFocusedEditableNode(child)
                if (result != null) {
                    child.recycle()
                    return result
                }
                child.recycle()
            }
        }

        return null
    }

    private fun getTextSelectionFromNode(node: AccessibilityNodeInfo): String {
        return try {
            val text = node.text?.toString() ?: ""
            // For now, return the full text. In a more advanced implementation,
            // we could try to get the actual selection bounds
            text
        } catch (e: Exception) {
            Log.e(TAG, "Error getting text selection", e)
            ""
        }
    }
    
    // Public methods for external access
    fun getLastSelectedText(): String = lastSelectedText
    
    fun getCurrentSelectedText(): String {
        try {
            val rootNode = rootInActiveWindow
            return getSelectedTextFromNode(rootNode) ?: lastSelectedText
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current selected text", e)
            return lastSelectedText
        }
    }
    
    fun translateAndCopyText(callback: (Boolean, String) -> Unit) {
        try {
            Log.d(TAG, "translateAndCopyText called")

            // Get text to translate with maximum safety
            val textToTranslate = getTextToTranslateSafely()
            Log.d(TAG, "Text to translate: '$textToTranslate'")

            if (textToTranslate.isEmpty()) {
                callback(false, "Please select and copy text first, then tap the translation button.")
                return
            }

            // Perform translation with maximum safety
            val translatedText = performSafeTranslation(textToTranslate)
            Log.d(TAG, "Translation result: '$translatedText'")

            // Copy to clipboard safely
            val copySuccess = copyToClipboardSafely(translatedText)

            if (copySuccess) {
                callback(true, "✓ Translated: '$translatedText'")
            } else {
                callback(false, "Translation completed but failed to copy to clipboard")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Critical error in translateAndCopyText", e)
            callback(false, "Translation service error. Please try again.")
        }
    }

    private fun getTextToTranslateSafely(): String {
        return try {
            // Method 1: Try clipboard first (most reliable)
            val clipboardText = getClipboardTextSafely()
            if (clipboardText.isNotEmpty() && clipboardText.length >= 2 &&
                !clipboardText.startsWith("Tamil Translation") &&
                !clipboardText.startsWith("✓ Translated:")) {
                return clipboardText.take(200) // Limit length
            }

            // Method 2: Try last selected text
            if (lastSelectedText.isNotEmpty() && lastSelectedText.length >= 2) {
                return lastSelectedText.take(200)
            }

            // Method 3: Return empty if nothing found
            return ""
        } catch (e: Exception) {
            Log.e(TAG, "Error getting text to translate", e)
            return ""
        }
    }

    private fun getClipboardTextSafely(): String {
        return try {
            val clipData = clipboardManager?.primaryClip
            if (clipData != null && clipData.itemCount > 0) {
                val text = clipData.getItemAt(0).text?.toString() ?: ""
                return text.trim()
            }
            return ""
        } catch (e: Exception) {
            Log.e(TAG, "Error getting clipboard text", e)
            return ""
        }
    }

    private fun performSafeTranslation(input: String): String {
        return try {
            // Use simple built-in translation to avoid native library issues
            getBuiltInTranslation(input)
        } catch (e: Exception) {
            Log.e(TAG, "Error in translation", e)
            "Translation error for: $input"
        }
    }

    private fun getBuiltInTranslation(input: String): String {
        val lowerInput = input.lowercase().trim()

        // Common words
        val translations = mapOf(
            "hello" to "வணக்கம்",
            "hi" to "வணக்கம்",
            "good" to "நல்ல",
            "morning" to "காலை",
            "evening" to "மாலை",
            "night" to "இரவு",
            "thank you" to "நன்றி",
            "thanks" to "நன்றி",
            "yes" to "ஆம்",
            "no" to "இல்லை",
            "water" to "தண்ணீர்",
            "food" to "உணவு",
            "love" to "அன்பு",
            "friend" to "நண்பன்",
            "family" to "குடும்பம்",
            "home" to "வீடு",
            "work" to "வேலை",
            "school" to "பள்ளி",
            "book" to "புத்தகம்",
            "time" to "நேரம்"
        )

        return translations[lowerInput] ?: "Tamil: $input"
    }

    private fun copyToClipboardSafely(text: String): Boolean {
        return try {
            val clipData = ClipData.newPlainText("Tamil Translation", text)
            clipboardManager?.setPrimaryClip(clipData)
            Log.d(TAG, "Successfully copied to clipboard")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to copy to clipboard", e)
            false
        }
    }

    private fun getClipboardText(): String {
        return try {
            val clipData = clipboardManager?.primaryClip
            if (clipData != null && clipData.itemCount > 0) {
                clipData.getItemAt(0).text?.toString() ?: ""
            } else {
                ""
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting clipboard text", e)
            ""
        }
    }
    
    fun translateAndReplaceText(callback: (Boolean, String) -> Unit) {
        val textToTranslate = getCurrentSelectedText()
        if (textToTranslate.isEmpty()) {
            callback(false, "No text selected")
            return
        }
        
        try {
            val translatedText = TranslateModule.translateText(textToTranslate)
            
            // Try to replace text (limited support on Android)
            val rootNode = rootInActiveWindow
            val success = replaceTextInNode(rootNode, translatedText)
            
            if (success) {
                callback(true, "Text replaced with translation")
            } else {
                // Fallback: copy to clipboard
                val clipData = ClipData.newPlainText("Tamil Translation", translatedText)
                clipboardManager?.setPrimaryClip(clipData)
                callback(true, "Text replacement not supported, translation copied to clipboard")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error replacing text", e)
            callback(false, "Text replacement failed: ${e.message}")
        }
    }
    
    private fun replaceTextInNode(node: AccessibilityNodeInfo?, newText: String): Boolean {
        if (node == null) return false
        
        try {
            // Try to set text directly
            val arguments = Bundle()
            arguments.putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, newText)
            
            if (node.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)) {
                return true
            }
            
            // Alternative approach: select all and paste (API 18+)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                val selectAllAction = 131072 // AccessibilityNodeInfo.ACTION_SELECT_ALL
                val cutAction = 65536 // AccessibilityNodeInfo.ACTION_CUT
                val pasteAction = 32768 // AccessibilityNodeInfo.ACTION_PASTE

                if (node.performAction(selectAllAction) && node.performAction(cutAction)) {
                    val clipData = ClipData.newPlainText("Translation", newText)
                    clipboardManager?.setPrimaryClip(clipData)

                    return node.performAction(pasteAction)
                }
            }
            
            return false
        } catch (e: Exception) {
            Log.e(TAG, "Error replacing text in node", e)
            return false
        } finally {
            node?.recycle()
        }
    }
}
