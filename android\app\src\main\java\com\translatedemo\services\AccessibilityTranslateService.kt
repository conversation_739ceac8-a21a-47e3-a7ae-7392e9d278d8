package com.translatedemo.services

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import com.translatedemo.TranslateModule

class AccessibilityTranslateService : AccessibilityService() {
    
    companion object {
        private const val TAG = "AccessibilityTranslateService"
        private var instance: AccessibilityTranslateService? = null
        
        fun getInstance(): AccessibilityTranslateService? = instance
        
        fun isServiceEnabled(): Boolean = instance != null
    }
    
    private var lastSelectedText: String = ""
    private var clipboardManager: ClipboardManager? = null
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        clipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        Log.d(TAG, "Accessibility service created")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
        Log.d(TAG, "Accessibility service destroyed")
    }
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        Log.d(TAG, "Accessibility service connected")
        
        val info = AccessibilityServiceInfo().apply {
            eventTypes = AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED or
                        AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED or
                        AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED
            
            feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC
            flags = AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS or
                   AccessibilityServiceInfo.FLAG_RETRIEVE_INTERACTIVE_WINDOWS
            
            notificationTimeout = 100
        }
        
        serviceInfo = info
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        event?.let { handleAccessibilityEvent(it) }
    }
    
    override fun onInterrupt() {
        Log.d(TAG, "Accessibility service interrupted")
    }
    
    private fun handleAccessibilityEvent(event: AccessibilityEvent) {
        when (event.eventType) {
            AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED -> {
                handleTextSelection(event)
            }
            AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED -> {
                // Handle text changes if needed
            }
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                // Handle window content changes if needed
            }
        }
    }
    
    private fun handleTextSelection(event: AccessibilityEvent) {
        try {
            val selectedText = getSelectedText(event)
            if (selectedText.isNotEmpty() && selectedText != lastSelectedText) {
                lastSelectedText = selectedText
                Log.d(TAG, "Text selected: $selectedText")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling text selection", e)
        }
    }
    
    private fun getSelectedText(event: AccessibilityEvent): String {
        // Try to get selected text from the event
        val fromIndex = event.fromIndex
        val toIndex = event.toIndex
        val text = event.text?.firstOrNull()?.toString() ?: ""
        
        return if (fromIndex >= 0 && toIndex > fromIndex && toIndex <= text.length) {
            text.substring(fromIndex, toIndex)
        } else {
            // Fallback: try to get selected text from accessibility node
            getSelectedTextFromNode(event.source)
        }
    }
    
    private fun getSelectedTextFromNode(node: AccessibilityNodeInfo?): String {
        if (node == null) return ""
        
        try {
            // Try to get selected text from the node
            val text = node.text?.toString() ?: ""
            val extras = Bundle()
            
            // Try to get text selection
            if (node.performAction(AccessibilityNodeInfo.ACTION_COPY, extras)) {
                // Text was copied, get it from clipboard
                val clipData = clipboardManager?.primaryClip
                if (clipData != null && clipData.itemCount > 0) {
                    return clipData.getItemAt(0).text?.toString() ?: ""
                }
            }
            
            return text
        } catch (e: Exception) {
            Log.e(TAG, "Error getting selected text from node", e)
            return ""
        } finally {
            node.recycle()
        }
    }
    
    // Public methods for external access
    fun getLastSelectedText(): String = lastSelectedText
    
    fun getCurrentSelectedText(): String {
        try {
            val rootNode = rootInActiveWindow
            return getSelectedTextFromNode(rootNode) ?: lastSelectedText
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current selected text", e)
            return lastSelectedText
        }
    }
    
    fun translateAndCopyText(callback: (Boolean, String) -> Unit) {
        val textToTranslate = getCurrentSelectedText()
        if (textToTranslate.isEmpty()) {
            callback(false, "No text selected")
            return
        }
        
        try {
            val translatedText = TranslateModule.translateText(textToTranslate)
            
            // Copy to clipboard
            val clipData = ClipData.newPlainText("Tamil Translation", translatedText)
            clipboardManager?.setPrimaryClip(clipData)
            
            callback(true, "Translation copied to clipboard")
        } catch (e: Exception) {
            Log.e(TAG, "Error translating text", e)
            callback(false, "Translation failed: ${e.message}")
        }
    }
    
    fun translateAndReplaceText(callback: (Boolean, String) -> Unit) {
        val textToTranslate = getCurrentSelectedText()
        if (textToTranslate.isEmpty()) {
            callback(false, "No text selected")
            return
        }
        
        try {
            val translatedText = TranslateModule.translateText(textToTranslate)
            
            // Try to replace text (limited support on Android)
            val rootNode = rootInActiveWindow
            val success = replaceTextInNode(rootNode, translatedText)
            
            if (success) {
                callback(true, "Text replaced with translation")
            } else {
                // Fallback: copy to clipboard
                val clipData = ClipData.newPlainText("Tamil Translation", translatedText)
                clipboardManager?.setPrimaryClip(clipData)
                callback(true, "Text replacement not supported, translation copied to clipboard")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error replacing text", e)
            callback(false, "Text replacement failed: ${e.message}")
        }
    }
    
    private fun replaceTextInNode(node: AccessibilityNodeInfo?, newText: String): Boolean {
        if (node == null) return false
        
        try {
            // Try to set text directly
            val arguments = Bundle()
            arguments.putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, newText)
            
            if (node.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)) {
                return true
            }
            
            // Alternative approach: select all and paste
            if (node.performAction(AccessibilityNodeInfo.ACTION_SELECT_ALL) &&
                node.performAction(AccessibilityNodeInfo.ACTION_CUT)) {
                
                val clipData = ClipData.newPlainText("Translation", newText)
                clipboardManager?.setPrimaryClip(clipData)
                
                return node.performAction(AccessibilityNodeInfo.ACTION_PASTE)
            }
            
            return false
        } catch (e: Exception) {
            Log.e(TAG, "Error replacing text in node", e)
            return false
        } finally {
            node?.recycle()
        }
    }
}
